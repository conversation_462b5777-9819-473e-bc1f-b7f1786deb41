using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Tests;

public static class EnhancedMarketAnalysisTest
{
    private static IServiceProvider _serviceProvider = null!;
    private static Microsoft.Extensions.Logging.ILogger _logger = null!;

    public static async Task RunEnhancedMarketAnalysisTest()
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/enhanced-market-analysis-test-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        try
        {
            // Setup DI container
            var host = CreateHostBuilder().Build();
            _serviceProvider = host.Services;
            _logger = _serviceProvider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<MarketRegimeAnalyzer>>();

            await TestEnhancedMarketAnalysis();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Enhanced market analysis test failed: {ex.Message}");
            Log.Fatal(ex, "Enhanced market analysis test failed");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static IHostBuilder CreateHostBuilder() =>
        Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddEnvironmentVariables();
            })
            .ConfigureServices((context, services) =>
            {
                // Register services
                services.AddSingleton<ISecurityService, SecurityService>();
                services.AddSingleton<IConfigurationValidator, ConfigurationValidator>();
                services.AddSingleton<IGlobalExceptionHandler, GlobalExceptionHandler>();
                services.AddSingleton<IAlpacaService, AlpacaService>();
                services.AddSingleton<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();
                services.AddSingleton<IOptionsScanner, OptionsScanner>();

                // Add logging
                services.AddLogging(builder =>
                {
                    builder.ClearProviders();
                    builder.AddSerilog();
                });
            });

    public async static Task TestEnhancedMarketAnalysis()
    {
        Console.WriteLine("=== Enhanced Market Analysis Test ===\n");

        try
        {
            var marketAnalyzer = _serviceProvider.GetRequiredService<IMarketRegimeAnalyzer>();
            var optionsScanner = _serviceProvider.GetRequiredService<IOptionsScanner>();

            // Test 1: Volatility Forecasting
            Console.WriteLine("1. Testing Enhanced Volatility Forecasting...");
            var volForecast = await marketAnalyzer.GetVolatilityForecastAsync("SPY", 5);
            
            Console.WriteLine($"   Symbol: {volForecast.Symbol}");
            Console.WriteLine($"   Current Volatility: {volForecast.CurrentVolatility:F2}%");
            Console.WriteLine($"   Forecasted Volatility: {volForecast.ForecastedVolatility:F2}%");
            Console.WriteLine($"   Volatility Trend: {volForecast.VolatilityTrend:F3}");
            Console.WriteLine($"   Confidence: {volForecast.Confidence:P1}");
            Console.WriteLine($"   Model: {volForecast.Model}");
            Console.WriteLine($"   Forecast Path: [{string.Join(", ", volForecast.VolatilityPath.Take(3).Select(v => v.ToString("F1")))}...]");

            // Test 2: Market Microstructure Analysis
            Console.WriteLine("\n2. Testing Market Microstructure Analysis...");
            var microstructure = await marketAnalyzer.GetMarketMicrostructureAsync("SPY");
            
            Console.WriteLine($"   Symbol: {microstructure.Symbol}");
            Console.WriteLine($"   VWAP: {microstructure.VolumeWeightedAveragePrice:F2}");
            Console.WriteLine($"   Bid-Ask Spread: {microstructure.BidAskSpread:P2}");
            Console.WriteLine($"   Order Flow Imbalance: {microstructure.OrderFlowImbalance:F4}");
            Console.WriteLine($"   Liquidity Score: {microstructure.LiquidityScore:F1}");
            Console.WriteLine($"   Momentum Score: {microstructure.MomentumScore:F2}%");
            Console.WriteLine($"   Market Impact: {microstructure.MarketImpact:F6}");
            
            if (microstructure.TechnicalIndicators.Any())
            {
                Console.WriteLine("   Technical Indicators:");
                foreach (var indicator in microstructure.TechnicalIndicators)
                {
                    Console.WriteLine($"     {indicator.Key}: {indicator.Value:F2}");
                }
            }

            // Test 3: Regime Transition Probability
            Console.WriteLine("\n3. Testing Regime Transition Probability...");
            var regimeTransition = await marketAnalyzer.GetRegimeTransitionProbabilityAsync();
            
            Console.WriteLine($"   Current Regime: {regimeTransition.CurrentRegime}");
            Console.WriteLine($"   Days in Current Regime: {regimeTransition.DaysInCurrentRegime}");
            Console.WriteLine($"   Regime Stability: {regimeTransition.RegimeStability:P1}");
            Console.WriteLine($"   Transition Signal: {regimeTransition.TransitionSignal:F3}");
            
            if (regimeTransition.TransitionProbabilities.Any())
            {
                Console.WriteLine("   Transition Probabilities:");
                foreach (var prob in regimeTransition.TransitionProbabilities)
                {
                    Console.WriteLine($"     To {prob.Key}: {prob.Value:P1}");
                }
            }

            // Test 4: Multi-Timeframe Analysis
            Console.WriteLine("\n4. Testing Multi-Timeframe Analysis...");
            var multiTimeframe = await marketAnalyzer.GetMultiTimeframeAnalysisAsync("SPY");
            
            Console.WriteLine($"   Symbol: {multiTimeframe.Symbol}");
            Console.WriteLine($"   Overall Momentum: {multiTimeframe.OverallMomentum:F3}");
            Console.WriteLine($"   Trend Alignment: {multiTimeframe.TrendAlignment:F3}");
            Console.WriteLine($"   Dominant Timeframe: {multiTimeframe.DominantTimeframe}");
            Console.WriteLine($"   Signal Strength: {multiTimeframe.SignalStrength:F3}");
            
            if (multiTimeframe.TimeframeTrends.Any())
            {
                Console.WriteLine("   Timeframe Analysis:");
                foreach (var timeframe in multiTimeframe.TimeframeTrends)
                {
                    Console.WriteLine($"     {timeframe.Key}: {timeframe.Value.Trend} (Strength: {timeframe.Value.Strength:F2}, Momentum: {timeframe.Value.Momentum:F2}%)");
                }
            }

            // Test 5: Market Stress Indicators
            Console.WriteLine("\n5. Testing Market Stress Indicators...");
            var stressIndicators = await marketAnalyzer.GetMarketStressIndicatorsAsync();
            
            Console.WriteLine($"   VIX Level: {stressIndicators.VixLevel:F2}");
            Console.WriteLine($"   Overall Stress Level: {stressIndicators.StressLevel:F1}/100");
            
            if (stressIndicators.StressFactors.Any())
            {
                Console.WriteLine("   Stress Factors:");
                foreach (var factor in stressIndicators.StressFactors)
                {
                    Console.WriteLine($"     - {factor}");
                }
            }

            // Test 6: Volatility Surface
            Console.WriteLine("\n6. Testing Volatility Surface Analysis...");
            var volSurface = await marketAnalyzer.GetVolatilitySurfaceAsync("SPY");
            
            Console.WriteLine($"   Symbol: {volSurface.Symbol}");
            Console.WriteLine($"   ATM Volatility: {volSurface.AtmVolatility:F2}%");
            Console.WriteLine($"   Volatility Skew: {volSurface.VolatilitySkew:F3}");
            Console.WriteLine($"   Term Structure Slope: {volSurface.TermStructureSlope:F3}");

            // Test 7: Phase 1 Enhanced Market Sentiment Analysis
            Console.WriteLine("\n7. Testing Phase 1 Market Sentiment Analysis...");
            var sentiment = await marketAnalyzer.GetMarketSentimentAsync("SPY");

            Console.WriteLine($"   Symbol: {sentiment.Symbol}");
            Console.WriteLine($"   Put/Call Ratio: {sentiment.PutCallRatio:F2}");
            Console.WriteLine($"   Put/Call Ratio MA: {sentiment.PutCallRatioMA:F2}");
            Console.WriteLine($"   VIX Sentiment: {sentiment.VixSentiment:F2}");
            Console.WriteLine($"   Options Flow Sentiment: {sentiment.OptionsFlowSentiment:F2}");
            Console.WriteLine($"   Market Breadth Sentiment: {sentiment.MarketBreadthSentiment:F2}");
            Console.WriteLine($"   Composite Sentiment Score: {sentiment.CompositeSentimentScore:F1}");
            Console.WriteLine($"   Sentiment Regime: {sentiment.SentimentRegime}");

            if (sentiment.SentimentFactors.Any())
            {
                Console.WriteLine("   Sentiment Factors:");
                foreach (var factor in sentiment.SentimentFactors)
                {
                    Console.WriteLine($"     - {factor}");
                }
            }

            // Test 8: Phase 1 Options Flow Analysis
            Console.WriteLine("\n8. Testing Phase 1 Options Flow Analysis...");
            var optionsFlow = await marketAnalyzer.GetOptionsFlowAnalysisAsync("SPY");

            Console.WriteLine($"   Symbol: {optionsFlow.Symbol}");
            Console.WriteLine($"   Call Volume: {optionsFlow.CallVolume:N0}");
            Console.WriteLine($"   Put Volume: {optionsFlow.PutVolume:N0}");
            Console.WriteLine($"   Call Open Interest: {optionsFlow.CallOpenInterest:N0}");
            Console.WriteLine($"   Put Open Interest: {optionsFlow.PutOpenInterest:N0}");
            Console.WriteLine($"   Unusual Activity Score: {optionsFlow.UnusualActivityScore:F1}");
            Console.WriteLine($"   Institutional Flow Score: {optionsFlow.InstitutionalFlowScore:F1}");
            Console.WriteLine($"   Dark Pool Flow: {optionsFlow.DarkPoolFlow:F2}");
            Console.WriteLine($"   Options Flow Momentum: {optionsFlow.OptionsFlowMomentum:F1}");

            if (optionsFlow.UnusualActivity.Any())
            {
                Console.WriteLine($"   Unusual Activities Found: {optionsFlow.UnusualActivity.Count}");
                foreach (var activity in optionsFlow.UnusualActivity.Take(3))
                {
                    Console.WriteLine($"     - {activity.OptionType} {activity.StrikePrice} (Score: {activity.UnusualityScore:F0})");
                }
            }

            // Test 9: Phase 1 Enhanced Volatility Regime Detection
            Console.WriteLine("\n9. Testing Phase 1 Enhanced Volatility Regime Detection...");
            var volRegime = await marketAnalyzer.GetEnhancedVolatilityRegimeAsync("SPY");

            Console.WriteLine($"   Symbol: {volRegime.Symbol}");
            Console.WriteLine($"   Current Regime: {volRegime.CurrentRegime}");
            Console.WriteLine($"   Predicted Regime: {volRegime.PredictedRegime}");
            Console.WriteLine($"   Regime Confidence: {volRegime.RegimeConfidence:P1}");
            Console.WriteLine($"   GARCH Volatility: {volRegime.GarchVolatility:F2}%");
            Console.WriteLine($"   Volatility Clustering Score: {volRegime.VolatilityClusteringScore:F1}");
            Console.WriteLine($"   Volatility Breakout Probability: {volRegime.VolatilityBreakoutProbability:F1}%");

            if (volRegime.RegimeSignals.Any())
            {
                Console.WriteLine("   Regime Signals:");
                foreach (var signal in volRegime.RegimeSignals)
                {
                    Console.WriteLine($"     - {signal.SignalType}: {signal.Description} (Strength: {signal.Strength:F2})");
                }
            }

            // Test 10: Phase 1 Market Breadth Analysis
            Console.WriteLine("\n10. Testing Phase 1 Market Breadth Analysis...");
            var breadth = await marketAnalyzer.GetMarketBreadthAsync();

            Console.WriteLine($"   Advance/Decline Ratio: {breadth.AdvanceDeclineRatio:F2}");
            Console.WriteLine($"   New Highs/New Lows: {breadth.NewHighsNewLows:F2}");
            Console.WriteLine($"   Up Volume/Down Volume: {breadth.UpVolumeDownVolume:F2}");
            Console.WriteLine($"   McClellan Oscillator: {breadth.McClellanOscillator:F1}");
            Console.WriteLine($"   Breadth Thrust: {breadth.BreadthThrust:F1}");
            Console.WriteLine($"   Sector Rotation Score: {breadth.SectorRotationScore:F1}");
            Console.WriteLine($"   Breadth Regime: {breadth.BreadthRegime}");

            if (breadth.BreadthSignals.Any())
            {
                Console.WriteLine("   Breadth Signals:");
                foreach (var signal in breadth.BreadthSignals)
                {
                    Console.WriteLine($"     - {signal}");
                }
            }

            // Test 11: Phase 1 Correlation Breakdown Detection
            Console.WriteLine("\n11. Testing Phase 1 Correlation Breakdown Detection...");
            var symbols = new List<string> { "SPY", "QQQ", "IWM", "DIA" };
            var correlationAlert = await marketAnalyzer.CheckCorrelationBreakdownAsync(symbols);

            Console.WriteLine($"   Symbols Analyzed: {string.Join(", ", correlationAlert.Symbols)}");
            Console.WriteLine($"   Breakdown Detected: {correlationAlert.IsBreakdownDetected}");
            Console.WriteLine($"   Alert Severity: {correlationAlert.AlertSeverity:F1}");
            Console.WriteLine($"   Correlation Change: {correlationAlert.CorrelationChange:F3}");

            if (!string.IsNullOrEmpty(correlationAlert.AlertMessage))
            {
                Console.WriteLine($"   Alert Message: {correlationAlert.AlertMessage}");
            }

            // Test 12: Phase 1 Volatility Spike Detection
            Console.WriteLine("\n12. Testing Phase 1 Volatility Spike Detection...");
            var spikeAlert = await marketAnalyzer.CheckVolatilitySpikeAsync("SPY");

            Console.WriteLine($"   Symbol: {spikeAlert.Symbol}");
            Console.WriteLine($"   Spike Detected: {spikeAlert.IsSpikeDetected}");
            Console.WriteLine($"   Current Volatility: {spikeAlert.CurrentVolatility:F2}%");
            Console.WriteLine($"   Normal Volatility: {spikeAlert.NormalVolatility:F2}%");
            Console.WriteLine($"   Spike Intensity: {spikeAlert.SpikeIntensity:F2}x");

            if (spikeAlert.IsSpikeDetected)
            {
                Console.WriteLine($"   Spike Type: {spikeAlert.SpikeType}");
                Console.WriteLine($"   Alert Message: {spikeAlert.AlertMessage}");
            }

            // Test 13: Phase 1 Unusual Options Activity Detection
            Console.WriteLine("\n13. Testing Phase 1 Unusual Options Activity Detection...");
            var unusualActivities = await marketAnalyzer.GetUnusualOptionsActivityAsync(new List<string> { "SPY", "QQQ" });

            Console.WriteLine($"   Total Unusual Activities Found: {unusualActivities.Count}");

            foreach (var activity in unusualActivities.Take(5))
            {
                Console.WriteLine($"   - {activity.Symbol} {activity.OptionType} {activity.StrikePrice} " +
                                $"(Volume: {activity.Volume:N0}, Ratio: {activity.VolumeRatio:F1}x, Score: {activity.UnusualityScore:F0})");
            }

            Console.WriteLine("\n=== Phase 1 Enhanced Market Analysis Test Completed Successfully ===");

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Enhanced market analysis test failed: {ex.Message}");
            _logger.LogError(ex, "Enhanced market analysis test failed");
        }
    }
}
