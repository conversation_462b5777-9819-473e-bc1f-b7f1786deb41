using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public static class EnhancedOptionsPricingTest
{
    private static ServiceProvider? _serviceProvider;
    private static ILogger? _logger;

    public static async Task RunEnhancedOptionsPricingTest()
    {
        Console.WriteLine("=== Enhanced Options Pricing and Greeks Test ===\n");

        // Setup
        SetupServices();
        _logger = _serviceProvider!.GetRequiredService<ILoggerFactory>().CreateLogger("EnhancedOptionsPricingTest");

        try
        {
            // Test 1: Black-Scholes Pricing Accuracy
            await TestBlackScholesPricing();

            // Test 2: Greeks Calculations
            await TestGreeksCalculations();

            // Test 3: Implied Volatility Calculation
            await TestImpliedVolatilityCalculation();

            // Test 4: Fair Value Analysis
            await TestFairValueAnalysis();

            // Test 5: Expected Move Calculations
            await TestExpectedMoveCalculations();

            // Test 6: Integration with Signal Quality
            await TestSignalQualityIntegration();

            Console.WriteLine("\n=== Enhanced Options Pricing Test Completed Successfully ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Test failed with error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    private static async Task TestBlackScholesPricing()
    {
        Console.WriteLine("1. Testing Black-Scholes Pricing Accuracy...");
        
        var optionsScanner = _serviceProvider!.GetRequiredService<IOptionsScanner>();
        
        // Create test option contracts
        var testOptions = new List<OptionContract>
        {
            new()
            {
                Symbol = "SPY240315C00500000",
                UnderlyingSymbol = "SPY",
                ExpirationDate = DateTime.Today.AddHours(6), // 0 DTE with 6 hours left
                StrikePrice = 500m,
                OptionType = OptionType.Call,
                Bid = 1.45m,
                Ask = 1.55m,
                Volume = 1000,
                OpenInterest = 5000
            },
            new()
            {
                Symbol = "SPY240315P00495000",
                UnderlyingSymbol = "SPY",
                ExpirationDate = DateTime.Today.AddHours(6),
                StrikePrice = 495m,
                OptionType = OptionType.Put,
                Bid = 0.95m,
                Ask = 1.05m,
                Volume = 800,
                OpenInterest = 3000
            }
        };

        var underlyingPrice = 498.50m;

        foreach (var option in testOptions)
        {
            try
            {
                var impliedVol = await optionsScanner.CalculateImpliedVolatilityAsync(option, underlyingPrice);
                var theoreticalPrice = await optionsScanner.CalculateTheoreticalPriceAsync(option, underlyingPrice, impliedVol);
                var isFairlyPriced = await optionsScanner.IsOptionFairlyPricedAsync(option, underlyingPrice);

                Console.WriteLine($"   {option.Symbol}:");
                Console.WriteLine($"     Market Price: {option.MidPrice:C2}");
                Console.WriteLine($"     Theoretical Price: {theoreticalPrice:C2}");
                Console.WriteLine($"     Implied Volatility: {impliedVol:P1}");
                Console.WriteLine($"     Fairly Priced: {isFairlyPriced}");
                Console.WriteLine($"     Price Difference: {Math.Abs(option.MidPrice - theoreticalPrice):C2}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Error testing {option.Symbol}: {ex.Message}");
            }
        }
    }

    private static async Task TestGreeksCalculations()
    {
        Console.WriteLine("\n2. Testing Greeks Calculations...");
        
        var optionsScanner = _serviceProvider!.GetRequiredService<IOptionsScanner>();
        
        var testOption = new OptionContract
        {
            Symbol = "SPY240315C00500000",
            UnderlyingSymbol = "SPY",
            ExpirationDate = DateTime.Today.AddHours(6),
            StrikePrice = 500m,
            OptionType = OptionType.Call,
            Bid = 1.45m,
            Ask = 1.55m
        };

        var underlyingPrice = 498.50m;

        try
        {
            var greeks = await optionsScanner.CalculateGreeksAsync(testOption, underlyingPrice);
            
            Console.WriteLine($"   Greeks for {testOption.Symbol}:");
            Console.WriteLine($"     Delta: {greeks.Delta:F4} (price sensitivity)");
            Console.WriteLine($"     Gamma: {greeks.Gamma:F4} (delta sensitivity)");
            Console.WriteLine($"     Theta: {greeks.Theta:F4} (time decay per day)");
            Console.WriteLine($"     Vega: {greeks.Vega:F4} (volatility sensitivity)");
            Console.WriteLine($"     Rho: {greeks.Rho:F4} (interest rate sensitivity)");

            // Validate Greeks ranges for 0 DTE options
            if (Math.Abs(greeks.Delta) > 1)
                Console.WriteLine($"   ⚠️ Warning: Delta out of range: {greeks.Delta}");
            
            if (greeks.Theta > 0)
                Console.WriteLine($"   ⚠️ Warning: Positive theta unusual for long options: {greeks.Theta}");
                
            Console.WriteLine($"   ✅ Greeks calculation completed");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ Error calculating Greeks: {ex.Message}");
        }
    }

    private static async Task TestImpliedVolatilityCalculation()
    {
        Console.WriteLine("\n3. Testing Implied Volatility Calculation...");
        
        var optionsScanner = _serviceProvider!.GetRequiredService<IOptionsScanner>();
        
        // Test with different moneyness levels
        var testCases = new[]
        {
            new { Strike = 495m, Price = 4.50m, Description = "ITM Call" },
            new { Strike = 500m, Price = 1.50m, Description = "ATM Call" },
            new { Strike = 505m, Price = 0.25m, Description = "OTM Call" }
        };

        var underlyingPrice = 498.50m;

        foreach (var testCase in testCases)
        {
            var option = new OptionContract
            {
                Symbol = $"SPY240315C{testCase.Strike:00000000}",
                UnderlyingSymbol = "SPY",
                ExpirationDate = DateTime.Today.AddHours(6),
                StrikePrice = testCase.Strike,
                OptionType = OptionType.Call,
                Bid = testCase.Price - 0.05m,
                Ask = testCase.Price + 0.05m
            };

            try
            {
                var impliedVol = await optionsScanner.CalculateImpliedVolatilityAsync(option, underlyingPrice);
                var moneyness = underlyingPrice / testCase.Strike;
                
                Console.WriteLine($"   {testCase.Description} (Strike: {testCase.Strike}):");
                Console.WriteLine($"     Market Price: {option.MidPrice:C2}");
                Console.WriteLine($"     Moneyness: {moneyness:F3}");
                Console.WriteLine($"     Implied Vol: {impliedVol:P1}");
                
                // Validate reasonable IV ranges
                if (impliedVol < 0.05m || impliedVol > 3.0m)
                    Console.WriteLine($"   ⚠️ Warning: Unusual implied volatility: {impliedVol:P1}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Error with {testCase.Description}: {ex.Message}");
            }
        }
    }

    private static async Task TestFairValueAnalysis()
    {
        Console.WriteLine("\n4. Testing Fair Value Analysis...");
        
        var optionsScanner = _serviceProvider!.GetRequiredService<IOptionsScanner>();
        var underlyingPrice = 498.50m;
        
        // Test overpriced and underpriced scenarios
        var testOptions = new[]
        {
            new OptionContract
            {
                Symbol = "SPY240315C00500000",
                UnderlyingSymbol = "SPY",
                ExpirationDate = DateTime.Today.AddHours(6),
                StrikePrice = 500m,
                OptionType = OptionType.Call,
                Bid = 2.45m, // Potentially overpriced
                Ask = 2.55m
            },
            new OptionContract
            {
                Symbol = "SPY240315C00505000",
                UnderlyingSymbol = "SPY",
                ExpirationDate = DateTime.Today.AddHours(6),
                StrikePrice = 505m,
                OptionType = OptionType.Call,
                Bid = 0.05m, // Potentially underpriced
                Ask = 0.15m
            }
        };

        foreach (var option in testOptions)
        {
            try
            {
                var isFairlyPriced = await optionsScanner.IsOptionFairlyPricedAsync(option, underlyingPrice);
                var impliedVol = await optionsScanner.CalculateImpliedVolatilityAsync(option, underlyingPrice);
                var theoreticalPrice = await optionsScanner.CalculateTheoreticalPriceAsync(option, underlyingPrice, impliedVol);
                
                var priceDifference = option.MidPrice - theoreticalPrice;
                var percentDifference = theoreticalPrice > 0 ? (priceDifference / theoreticalPrice) * 100 : 0;
                
                Console.WriteLine($"   {option.Symbol}:");
                Console.WriteLine($"     Market: {option.MidPrice:C2}, Theoretical: {theoreticalPrice:C2}");
                Console.WriteLine($"     Difference: {priceDifference:C2} ({percentDifference:F1}%)");
                Console.WriteLine($"     Fairly Priced: {isFairlyPriced}");
                Console.WriteLine($"     Assessment: {(priceDifference > 0 ? "Overpriced" : "Underpriced")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Error analyzing {option.Symbol}: {ex.Message}");
            }
        }
    }

    private static async Task TestExpectedMoveCalculations()
    {
        Console.WriteLine("\n5. Testing Expected Move Calculations...");
        
        var optionsScanner = _serviceProvider!.GetRequiredService<IOptionsScanner>();
        
        var symbols = new[] { "SPY", "QQQ", "IWM" };
        var expirationDate = DateTime.Today.AddHours(6);

        foreach (var symbol in symbols)
        {
            try
            {
                var expectedMove = await optionsScanner.CalculateExpectedMoveAsync(symbol, expirationDate);
                
                Console.WriteLine($"   {symbol}:");
                Console.WriteLine($"     Expected Move: ±{expectedMove:F2}");
                Console.WriteLine($"     Time to Expiry: {(expirationDate - DateTime.Now).TotalHours:F1} hours");
                
                if (expectedMove > 0)
                {
                    Console.WriteLine($"     Expected Range: {500m - expectedMove:F2} - {500m + expectedMove:F2}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Error calculating expected move for {symbol}: {ex.Message}");
            }
        }
    }

    private static async Task TestSignalQualityIntegration()
    {
        Console.WriteLine("\n6. Testing Signal Quality Integration...");
        
        var optionsScanner = _serviceProvider!.GetRequiredService<IOptionsScanner>();
        
        // Create a test option chain
        var chain = new OptionChain
        {
            UnderlyingSymbol = "SPY",
            UnderlyingPrice = 498.50m,
            ExpirationDate = DateTime.Today,
            Calls = new List<OptionContract>
            {
                new()
                {
                    Symbol = "SPY240315C00500000",
                    StrikePrice = 500m,
                    OptionType = OptionType.Call,
                    Bid = 1.45m,
                    Ask = 1.55m,
                    Volume = 1000,
                    OpenInterest = 5000
                }
            },
            Puts = new List<OptionContract>
            {
                new()
                {
                    Symbol = "SPY240315P00495000",
                    StrikePrice = 495m,
                    OptionType = OptionType.Put,
                    Bid = 0.95m,
                    Ask = 1.05m,
                    Volume = 800,
                    OpenInterest = 3000
                }
            }
        };

        try
        {
            // Test finding opportunities with enhanced pricing
            var signals = await optionsScanner.FindTradingOpportunitiesAsync(new List<OptionChain> { chain });
            
            Console.WriteLine($"   Found {signals.Count} trading signals");
            
            foreach (var signal in signals.Take(2))
            {
                var qualityScore = await optionsScanner.CalculateSignalQualityScoreAsync(signal, chain);
                
                Console.WriteLine($"   Signal: {signal.Strategy}");
                Console.WriteLine($"     Quality Score: {qualityScore:F3}");
                Console.WriteLine($"     Expected Profit: {signal.ExpectedProfit:C2}");
                Console.WriteLine($"     Max Loss: {signal.MaxLoss:C2}");
                Console.WriteLine($"     Risk/Reward: {signal.RiskRewardRatio:F2}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ Error testing signal quality integration: {ex.Message}");
        }
    }

    private static void SetupServices()
    {
        var services = new ServiceCollection();
        
        // Configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true)
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Alpaca:ApiKey"] = "test_key",
                ["Alpaca:SecretKey"] = "test_secret",
                ["Alpaca:BaseUrl"] = "https://paper-api.alpaca.markets",
                ["Trading:MaxDailyLoss"] = "500",
                ["MarketRegime:VixLowThreshold"] = "20",
                ["MarketRegime:VixHighThreshold"] = "30"
            })
            .Build();
        
        services.AddSingleton<IConfiguration>(configuration);
        
        // Logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        
        // Services
        services.AddSingleton<ISecurityService, SecurityService>();
        services.AddSingleton<IConfigurationValidator, ConfigurationValidator>();
        services.AddSingleton<IGlobalExceptionHandler, GlobalExceptionHandler>();
        services.AddSingleton<IAlpacaService, AlpacaService>();
        services.AddSingleton<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();
        services.AddSingleton<IOptionsScanner, OptionsScanner>();
        
        _serviceProvider = services.BuildServiceProvider();
    }
}
