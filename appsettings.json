{"Alpaca": {"ApiKey": "AKR6SLIKSB0NCBL2CNLB", "SecretKey": "mgRw02d5XNabcUgopVmb22fDoCEVLsjs7QswywJz", "BaseUrl": "https://api.alpaca.markets", "DataUrl": "https://data.alpaca.markets"}, "Polygon": {"ApiKey": "********************************", "BaseUrl": "https://api.polygon.io"}, "Trading": {"PrimarySymbol": "SPX", "BackupSymbol": "SPY", "MaxPositionSize": 10000, "MaxDailyLoss": 500, "RiskPerTrade": 0.02, "MaxPositionsPerDay": 5, "MinDaysToExpiration": 0, "MaxDaysToExpiration": 0, "EntryTimeStart": "09:45:00", "EntryTimeEnd": "10:30:00", "ManagementTime": "14:00:00", "ForceCloseTime": "15:45:00", "TradingEndTime": "16:00:00", "MinAccountEquity": 2000, "MinBuyingPower": 1000, "ProfitTargetPercent": 0.5, "StopLossPercent": 2.0, "RiskRewardThreshold": 0.15}, "MarketRegime": {"VixLowThreshold": 18, "VixHighThreshold": 25, "TrendLookbackPeriods": 20, "VolatilityLookbackPeriods": 14, "VolatilityCalculationDays": 30, "VolatilityForecastLookback": 60, "ATRPeriods": 14, "RSIPeriods": 14, "BollingerBandPeriods": 20, "BollingerBandStdDev": 2.0, "GarchAlpha": 0.1, "GarchBeta": 0.85, "GarchOmega": 1e-05, "MicrostructureLookbackHours": 6, "MultiTimeframeEnabled": true, "RegimeTransitionSensitivity": 0.7, "VolatilitySpikeThreshold": 1.5, "CorrelationBreakdownThreshold": 0.3, "UnusualActivityVolumeThreshold": 5.0, "SentimentExtremeThreshold": 60, "MarketBreadthLookbackDays": 20, "OptionsFlowLookbackHours": 4}, "Risk": {"MaxDrawdown": 0.08, "VaRLimit": 0.03, "MaxConcentration": 0.6, "MaxCorrelatedExposure": 0.7, "PortfolioHeatLimit": 0.75, "MaxDailyTrades": 8, "MaxOpenPositions": 12, "StressTestMultiplier": 1.5, "RiskRewardMinimum": 0.15, "MaxPositionsPerSymbol": 4, "ConcentrationWarningLevel": 0.5}, "Strategies": {"PutCreditSpread": {"Enabled": true, "Priority": 1, "MinDelta": 0.05, "MaxDelta": 0.15, "MinPremium": 0.1, "MaxSpreadWidth": 10, "MinDaysToExpiration": 0, "MaxDaysToExpiration": 0, "ProfitTarget": 0.5, "StopLoss": 2.0}, "IronButterfly": {"Enabled": true, "Priority": 2, "ATMRange": 0.02, "WingWidth": 25, "MinPremium": 0.15, "ProfitTarget": 0.5, "StopLoss": 2.0}, "CallCreditSpread": {"Enabled": true, "Priority": 3, "MinDelta": 0.05, "MaxDelta": 0.15, "MinPremium": 0.1, "MaxSpreadWidth": 10, "ProfitTarget": 0.5, "StopLoss": 2.0}}, "MachineLearning": {"ModelUpdateIntervalHours": 24, "MinTrainingDataPoints": 100, "ConfidenceThreshold": 0.7, "SignalQualityWeights": {"ML": 0.4, "Technical": 0.3, "MarketCondition": 0.2, "Liquidity": 0.1}, "PredictionTimeframes": {"PriceDirection": "1h", "Volatility": "4h"}}, "Monitoring": {"UpdateIntervalMs": 5000, "AlertCheckIntervalMs": 10000, "HealthCheckIntervalMs": 30000, "MaxMetricsHistory": 1000, "NotificationChannels": {"Console": {"Enabled": true, "Priority": 1}, "Email": {"Enabled": false, "Priority": 2, "SmtpServer": "", "SmtpPort": 587, "Username": "", "Password": "", "ToAddress": "", "UseSsl": true}, "SMS": {"Enabled": false, "Priority": 1, "Provider": "<PERSON><PERSON><PERSON>", "AccountSid": "", "AuthToken": "", "FromNumber": "", "ToNumber": "", "Region": ""}, "Slack": {"Enabled": false, "Priority": 3, "WebhookUrl": "", "Channel": "#alerts", "Username": "Zero DTE Bot", "IconEmoji": ":warning:"}}}, "CircuitBreaker": {"AlpacaAPI": {"FailureThreshold": 5, "TimeoutMinutes": 5}, "OptionsData": {"FailureThreshold": 3, "TimeoutMinutes": 3}, "MarketData": {"FailureThreshold": 3, "TimeoutMinutes": 2}, "RiskManagement": {"FailureThreshold": 2, "TimeoutMinutes": 1}, "OrderExecution": {"FailureThreshold": 2, "TimeoutMinutes": 1}}, "Optimization": {"MinIntervalHours": 24, "MinWinRate": 0.6, "MinSharpe": 1.0, "MaxDrawdown": 0.1, "PortfolioOptimization": {"Enabled": true, "RebalanceIntervalHours": 168, "MaxAllocationPerStrategy": 0.6, "MinAllocationPerStrategy": 0.1}, "AdaptiveParameters": {"Enabled": true, "UpdateIntervalHours": 12, "PerformanceThreshold": 0.05}}, "MultiTimeframe": {"Enabled": true, "Timeframes": ["1m", "5m", "15m", "1h", "1d"], "CacheExpiryMinutes": 5, "ConflictResolution": "HigherTimeframePriority"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "ZeroDateStrat": "Debug"}}}