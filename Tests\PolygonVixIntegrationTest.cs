using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public static class PolygonVixIntegrationTest
{
    public static async Task RunPolygonVixIntegrationTest()
    {
        var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<Program>();
        
        try
        {
            logger.LogInformation("=== Polygon.io VIX Integration Test ===");
            
            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            // Check if Polygon API key is configured
            var polygonApiKey = configuration["Polygon:ApiKey"];
            if (string.IsNullOrEmpty(polygonApiKey) || polygonApiKey == "YOUR_POLYGON_API_KEY_HERE")
            {
                logger.LogError("❌ Polygon API key not configured. Please set your Polygon.io API key in appsettings.json");
                logger.LogInformation("To get a Polygon.io API key:");
                logger.LogInformation("1. Visit https://polygon.io/");
                logger.LogInformation("2. Sign up for a free account");
                logger.LogInformation("3. Get your API key from the dashboard");
                logger.LogInformation("4. Update appsettings.json: \"Polygon\": { \"ApiKey\": \"YOUR_API_KEY\" }");
                return;
            }

            // Build service provider
            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => builder.AddConsole());
            services.AddHttpClient<IPolygonDataService, PolygonDataService>();
            services.AddSingleton<IPolygonDataService, PolygonDataService>();
            services.AddSingleton<IAlpacaService, AlpacaService>();
            services.AddSingleton<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();
            services.AddSingleton<IEnhancedRiskManager, EnhancedRiskManager>();

            var serviceProvider = services.BuildServiceProvider();

            // Test 1: Polygon.io Connection
            logger.LogInformation("\n🔌 Testing Polygon.io Connection...");
            var polygonService = serviceProvider.GetRequiredService<IPolygonDataService>();
            
            var connectionTest = await polygonService.TestConnectionAsync();
            if (connectionTest)
            {
                logger.LogInformation("✅ Polygon.io connection successful");
            }
            else
            {
                logger.LogError("❌ Polygon.io connection failed");
                return;
            }

            // Test 2: VIX Data Retrieval
            logger.LogInformation("\n📊 Testing VIX Data Retrieval...");
            var currentVix = await polygonService.GetCurrentVixAsync();
            
            if (currentVix > 0 && currentVix < 100)
            {
                logger.LogInformation($"✅ Current VIX: {currentVix:F2}");
                
                // Interpret VIX level
                var vixInterpretation = currentVix switch
                {
                    < 12 => "Very Low (Complacency Risk)",
                    < 15 => "Low (Good for Premium Selling)",
                    < 20 => "Normal (Standard Trading)",
                    < 25 => "Elevated (Caution Advised)",
                    < 30 => "High (Defensive Strategies)",
                    < 35 => "Very High (Minimal Trading)",
                    _ => "Extreme (Emergency Mode)"
                };
                
                logger.LogInformation($"📈 VIX Level: {vixInterpretation}");
            }
            else
            {
                logger.LogWarning($"⚠️ VIX data seems unusual: {currentVix:F2}");
            }

            // Test 3: VIX Change Analysis
            logger.LogInformation("\n📈 Testing VIX Change Analysis...");
            var vixChange1h = await polygonService.GetVixChangeAsync(TimeSpan.FromHours(1));
            var vixChangeDaily = await polygonService.GetVixChangeAsync(TimeSpan.FromDays(1));
            
            logger.LogInformation($"📊 VIX Change (1 hour): {vixChange1h:+0.00;-0.00;0.00}");
            logger.LogInformation($"📊 VIX Change (1 day): {vixChangeDaily:+0.00;-0.00;0.00}");
            
            if (Math.Abs(vixChange1h) > 2)
            {
                logger.LogWarning($"⚠️ Significant VIX movement detected in last hour: {vixChange1h:F2}");
            }

            // Test 4: Enhanced Market Regime Analysis
            logger.LogInformation("\n🎯 Testing Enhanced Market Regime Analysis...");
            var marketRegimeAnalyzer = serviceProvider.GetRequiredService<IMarketRegimeAnalyzer>();
            var regime = await marketRegimeAnalyzer.GetCurrentRegimeAsync();
            
            logger.LogInformation($"📊 Market Regime: {regime.OverallRegime}");
            logger.LogInformation($"📊 Volatility Regime: {regime.VolatilityRegime}");
            logger.LogInformation($"📊 Market Trend: {regime.Trend}");
            logger.LogInformation($"📊 Confidence: {regime.Confidence:P1}");
            logger.LogInformation($"📊 Description: {regime.Description}");

            // Test 5: VIX-Based Position Sizing
            logger.LogInformation("\n💰 Testing VIX-Based Position Sizing...");
            var enhancedRiskManager = serviceProvider.GetRequiredService<IEnhancedRiskManager>();
            
            // Create a sample trading signal
            var sampleSignal = new TradingSignal
            {
                Id = "TEST_SIGNAL",
                Strategy = "PutCreditSpread",
                UnderlyingSymbol = "SPX",
                ExpectedProfit = 50,
                MaxLoss = 200,
                RiskRewardRatio = 0.25m,
                Confidence = 0.75m,
                ExpirationDate = DateTime.Today
            };

            var volatilityMultiplier = await enhancedRiskManager.GetVolatilityMultiplierAsync();
            var timeMultiplier = await enhancedRiskManager.GetTimeBasedMultiplierAsync();
            var adjustedPositionSize = await enhancedRiskManager.CalculateVixAdjustedPositionSizeAsync(sampleSignal);
            
            logger.LogInformation($"💰 Volatility Multiplier: {volatilityMultiplier:F2}x");
            logger.LogInformation($"⏰ Time Multiplier: {timeMultiplier:F2}x");
            logger.LogInformation($"💰 Adjusted Position Size: ${adjustedPositionSize:F0}");

            // Test 6: Dynamic Risk Adjustment
            logger.LogInformation("\n⚖️ Testing Dynamic Risk Adjustment...");
            var riskAdjustment = await enhancedRiskManager.GetDynamicRiskAdjustmentAsync();
            
            logger.LogInformation($"⚖️ Risk Level: {riskAdjustment.RiskLevel}");
            logger.LogInformation($"💸 Adjusted Daily Loss Limit: ${riskAdjustment.AdjustedDailyLossLimit:F0}");
            logger.LogInformation($"📊 Adjusted Max Positions: {riskAdjustment.AdjustedMaxPositions}");
            logger.LogInformation($"💡 Recommendation: {riskAdjustment.RecommendedAction}");

            // Test 7: VIX-Based Trade Validation
            logger.LogInformation("\n✅ Testing VIX-Based Trade Validation...");
            var tradeValid = await enhancedRiskManager.ValidateVixBasedTradeAsync(sampleSignal);
            
            if (tradeValid)
            {
                logger.LogInformation("✅ Sample trade passes VIX-based validation");
            }
            else
            {
                logger.LogWarning("❌ Sample trade rejected by VIX-based validation");
            }

            // Test 8: Position Sizing Scenarios
            logger.LogInformation("\n📊 Position Sizing Scenarios:");
            
            var scenarios = new[]
            {
                (vix: 12m, description: "Very Low VIX"),
                (vix: 18m, description: "Normal VIX"),
                (vix: 25m, description: "Elevated VIX"),
                (vix: 35m, description: "High VIX")
            };

            foreach (var scenario in scenarios)
            {
                var multiplier = scenario.vix switch
                {
                    < 12 => 1.5m,
                    < 15 => 1.3m,
                    < 18 => 1.1m,
                    < 22 => 1.0m,
                    < 25 => 0.8m,
                    < 30 => 0.6m,
                    < 35 => 0.4m,
                    _ => 0.2m
                };
                
                logger.LogInformation($"📊 {scenario.description} ({scenario.vix:F0}): {multiplier:F1}x position size");
            }

            // Test 9: Historical VIX Data (if available)
            logger.LogInformation("\n📈 Testing Historical VIX Data...");
            try
            {
                var endDate = DateTime.UtcNow.Date;
                var startDate = endDate.AddDays(-5);
                var vixHistory = await polygonService.GetVixHistoryAsync(startDate, endDate);
                
                if (vixHistory.Any())
                {
                    logger.LogInformation($"📊 Retrieved {vixHistory.Count} days of VIX history");
                    foreach (var point in vixHistory.TakeLast(3))
                    {
                        logger.LogInformation($"📅 {point.Date:yyyy-MM-dd}: VIX {point.Value:F2} (High: {point.High:F2}, Low: {point.Low:F2})");
                    }
                }
                else
                {
                    logger.LogInformation("📊 No historical VIX data available (may require paid Polygon plan)");
                }
            }
            catch (Exception ex)
            {
                logger.LogInformation($"📊 Historical VIX data not available: {ex.Message}");
            }

            // Summary
            logger.LogInformation("\n🎉 === Polygon.io VIX Integration Test Complete ===");
            logger.LogInformation("✅ All core VIX integration features tested successfully");
            logger.LogInformation("💡 The system can now use real VIX data for dynamic position sizing");
            logger.LogInformation("📈 Position sizes will automatically adjust based on market volatility");
            logger.LogInformation("⚖️ Risk management is now enhanced with real-time VIX monitoring");
            
            // Next steps
            logger.LogInformation("\n🚀 Next Steps:");
            logger.LogInformation("1. Update your Polygon.io API key in appsettings.json");
            logger.LogInformation("2. Test with live trading in paper mode first");
            logger.LogInformation("3. Monitor VIX-based position sizing in real trading");
            logger.LogInformation("4. Adjust VIX thresholds based on your risk tolerance");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Error in Polygon VIX integration test");
        }
    }
}
