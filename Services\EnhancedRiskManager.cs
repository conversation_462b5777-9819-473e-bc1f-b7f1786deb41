using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IEnhancedRiskManager : IRiskManager
{
    Task<decimal> CalculateVixAdjustedPositionSizeAsync(TradingSignal signal);
    Task<decimal> GetVolatilityMultiplierAsync();
    Task<decimal> GetTimeBasedMultiplierAsync();
    Task<RiskAdjustment> GetDynamicRiskAdjustmentAsync();
    Task<bool> ValidateVixBasedTradeAsync(TradingSignal signal);
}

public class EnhancedRiskManager : IEnhancedRiskManager
{
    private readonly ILogger<EnhancedRiskManager> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;
    private readonly IMarketRegimeAnalyzer _marketRegimeAnalyzer;
    private readonly IPolygonDataService _polygonDataService;

    public EnhancedRiskManager(
        ILogger<EnhancedRiskManager> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService,
        IMarketRegimeAnalyzer marketRegimeAnalyzer,
        IPolygonDataService polygonDataService)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
        _marketRegimeAnalyzer = marketRegimeAnalyzer;
        _polygonDataService = polygonDataService;
    }

    public async Task<decimal> CalculateVixAdjustedPositionSizeAsync(TradingSignal signal)
    {
        try
        {
            _logger.LogDebug($"Calculating VIX-adjusted position size for signal {signal.Id}");

            // Get base position size from original risk manager logic
            var basePositionSize = await CalculateBasePositionSizeAsync(signal);
            
            // Get VIX-based volatility multiplier
            var volatilityMultiplier = await GetVolatilityMultiplierAsync();
            
            // Get time-based multiplier for 0 DTE
            var timeMultiplier = await GetTimeBasedMultiplierAsync();
            
            // Get signal confidence multiplier
            var confidenceMultiplier = GetConfidenceMultiplier(signal.Confidence);
            
            // Calculate final adjusted position size
            var adjustedSize = basePositionSize * volatilityMultiplier * timeMultiplier * confidenceMultiplier;
            
            // Apply absolute limits
            var maxPositionSize = _configuration.GetValue<decimal>("Trading:MaxPositionSize", 10000);
            var minPositionSize = _configuration.GetValue<decimal>("Trading:MinPositionSize", 100);
            
            adjustedSize = Math.Max(minPositionSize, Math.Min(maxPositionSize, adjustedSize));
            
            _logger.LogInformation($"Position size calculation for {signal.Strategy}: Base={basePositionSize:C}, " +
                $"VIX Mult={volatilityMultiplier:F2}, Time Mult={timeMultiplier:F2}, " +
                $"Confidence Mult={confidenceMultiplier:F2}, Final={adjustedSize:C}");
            
            return adjustedSize;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating VIX-adjusted position size");
            return await CalculateBasePositionSizeAsync(signal);
        }
    }

    public async Task<decimal> GetVolatilityMultiplierAsync()
    {
        try
        {
            var vix = await _marketRegimeAnalyzer.GetVixAsync();
            
            // VIX-based position sizing multipliers
            var multiplier = vix switch
            {
                < 12 => 1.5m,   // Very low vol: increase size significantly
                < 15 => 1.3m,   // Low vol: increase size
                < 18 => 1.1m,   // Below average: slight increase
                < 22 => 1.0m,   // Normal vol: standard size
                < 25 => 0.8m,   // Elevated vol: reduce size
                < 30 => 0.6m,   // High vol: reduce significantly
                < 35 => 0.4m,   // Very high vol: minimal size
                _ => 0.2m       // Extreme vol: emergency minimal size
            };
            
            _logger.LogDebug($"VIX: {vix:F2}, Volatility multiplier: {multiplier:F2}");
            return multiplier;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating volatility multiplier");
            return 1.0m; // Default to no adjustment
        }
    }

    public async Task<decimal> GetTimeBasedMultiplierAsync()
    {
        try
        {
            var now = DateTime.Now.TimeOfDay;
            var marketOpen = TimeSpan.Parse(_configuration["Trading:MarketOpenTime"] ?? "09:30:00");
            var marketClose = TimeSpan.Parse(_configuration["Trading:MarketCloseTime"] ?? "16:00:00");
            
            // Calculate hours until market close for 0 DTE timing
            var hoursToClose = (marketClose - now).TotalHours;
            
            // Time-based multipliers for 0 DTE strategies
            var multiplier = hoursToClose switch
            {
                > 6 => 1.0m,    // Morning: full size
                > 5 => 0.9m,    // Late morning: slight reduction
                > 4 => 0.8m,    // Midday: reduce size
                > 3 => 0.6m,    // Early afternoon: significant reduction
                > 2 => 0.4m,    // Late afternoon: minimal size
                > 1 => 0.2m,    // Near close: emergency only
                _ => 0.1m       // Final hour: minimal emergency size
            };
            
            _logger.LogDebug($"Hours to close: {hoursToClose:F1}, Time multiplier: {multiplier:F2}");
            return multiplier;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating time-based multiplier");
            return 1.0m; // Default to no adjustment
        }
    }

    public async Task<RiskAdjustment> GetDynamicRiskAdjustmentAsync()
    {
        try
        {
            var vix = await _marketRegimeAnalyzer.GetVixAsync();
            var vixChange = await _polygonDataService.GetVixChangeAsync(TimeSpan.FromHours(1));
            var regime = await _marketRegimeAnalyzer.GetCurrentRegimeAsync();
            
            var adjustment = new RiskAdjustment
            {
                CurrentVix = vix,
                VixChange = vixChange,
                MarketRegime = regime.OverallRegime,
                Timestamp = DateTime.UtcNow
            };
            
            // Calculate dynamic daily loss limit
            var baseDailyLoss = _configuration.GetValue<decimal>("Trading:MaxDailyLoss", 500);
            adjustment.AdjustedDailyLossLimit = vix switch
            {
                < 15 => baseDailyLoss * 1.2m,  // Low vol: allow slightly higher risk
                < 20 => baseDailyLoss,         // Normal vol: standard limit
                < 30 => baseDailyLoss * 0.7m,  // High vol: reduce limit
                _ => baseDailyLoss * 0.4m      // Extreme vol: minimal limit
            };
            
            // Calculate dynamic max positions
            var baseMaxPositions = _configuration.GetValue<int>("Trading:MaxPositionsPerDay", 5);
            adjustment.AdjustedMaxPositions = vix switch
            {
                < 15 => baseMaxPositions + 2,  // Low vol: allow more positions
                < 20 => baseMaxPositions,      // Normal vol: standard
                < 30 => Math.Max(1, baseMaxPositions - 2), // High vol: fewer positions
                _ => 1                         // Extreme vol: maximum 1 position
            };
            
            // Risk level assessment
            if (vix > 30 || Math.Abs(vixChange) > 3)
            {
                adjustment.RiskLevel = RiskLevel.High;
                adjustment.RecommendedAction = "Reduce position sizes and limit new trades";
            }
            else if (vix > 25 || Math.Abs(vixChange) > 2)
            {
                adjustment.RiskLevel = RiskLevel.Medium;
                adjustment.RecommendedAction = "Use caution with position sizing";
            }
            else
            {
                adjustment.RiskLevel = RiskLevel.Low;
                adjustment.RecommendedAction = "Normal trading conditions";
            }
            
            _logger.LogInformation($"Dynamic risk adjustment: VIX={vix:F2}, Change={vixChange:F2}, " +
                $"Daily Limit={adjustment.AdjustedDailyLossLimit:C}, Max Positions={adjustment.AdjustedMaxPositions}");
            
            return adjustment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating dynamic risk adjustment");
            return new RiskAdjustment
            {
                CurrentVix = 20,
                RiskLevel = RiskLevel.Medium,
                RecommendedAction = "Error in risk calculation - use conservative approach"
            };
        }
    }

    public async Task<bool> ValidateVixBasedTradeAsync(TradingSignal signal)
    {
        try
        {
            var vix = await _marketRegimeAnalyzer.GetVixAsync();
            var vixChange = await _polygonDataService.GetVixChangeAsync(TimeSpan.FromHours(1));
            
            // Don't trade in extreme volatility conditions
            if (vix > 35)
            {
                _logger.LogWarning($"Trade rejected: VIX too high ({vix:F2})");
                return false;
            }
            
            // Don't trade during volatility spikes
            if (Math.Abs(vixChange) > 4)
            {
                _logger.LogWarning($"Trade rejected: VIX change too large ({vixChange:F2})");
                return false;
            }
            
            // Require higher confidence in high volatility
            var requiredConfidence = vix switch
            {
                < 15 => 0.6m,   // Low vol: standard confidence
                < 20 => 0.7m,   // Normal vol: higher confidence
                < 30 => 0.8m,   // High vol: high confidence required
                _ => 0.9m       // Extreme vol: very high confidence
            };
            
            if (signal.Confidence < requiredConfidence)
            {
                _logger.LogWarning($"Trade rejected: Confidence {signal.Confidence:F2} below required {requiredConfidence:F2} for VIX {vix:F2}");
                return false;
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in VIX-based trade validation");
            return false; // Conservative approach on error
        }
    }

    private async Task<decimal> CalculateBasePositionSizeAsync(TradingSignal signal)
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            var equity = account?.Equity ?? 0;
            
            var riskPerTrade = _configuration.GetValue<decimal>("Trading:RiskPerTrade", 0.02m);
            var maxRiskAmount = equity * riskPerTrade;
            
            // Calculate position size based on max loss
            var positionSize = signal.MaxLoss > 0 ? maxRiskAmount / signal.MaxLoss : 0;
            
            return Math.Max(100, positionSize); // Minimum $100 position
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating base position size");
            return 100; // Minimum fallback
        }
    }

    private decimal GetConfidenceMultiplier(decimal confidence)
    {
        // Adjust position size based on signal confidence
        return confidence switch
        {
            >= 0.9m => 1.2m,   // Very high confidence: increase size
            >= 0.8m => 1.1m,   // High confidence: slight increase
            >= 0.7m => 1.0m,   // Good confidence: standard size
            >= 0.6m => 0.8m,   // Medium confidence: reduce size
            >= 0.5m => 0.6m,   // Low confidence: significant reduction
            _ => 0.4m          // Very low confidence: minimal size
        };
    }

    // Implement IRiskManager interface methods
    public async Task<RiskValidationResult> ValidateTradeAsync(TradingSignal signal)
    {
        // Enhanced validation using VIX data
        var vixValid = await ValidateVixBasedTradeAsync(signal);
        if (!vixValid)
        {
            return new RiskValidationResult
            {
                IsApproved = false,
                RejectionReason = "VIX-based risk validation failed"
            };
        }

        // Continue with standard risk validation...
        // (Implementation would include all standard risk checks)
        return new RiskValidationResult { IsApproved = true };
    }

    public async Task<PortfolioRisk> CalculatePortfolioRiskAsync()
    {
        // Enhanced portfolio risk calculation with VIX integration
        var adjustment = await GetDynamicRiskAdjustmentAsync();
        
        return new PortfolioRisk
        {
            CurrentVix = adjustment.CurrentVix,
            RiskLevel = adjustment.RiskLevel,
            AdjustedDailyLossLimit = adjustment.AdjustedDailyLossLimit,
            Timestamp = DateTime.UtcNow
        };
    }
}

// New models for enhanced risk management
public class RiskAdjustment
{
    public decimal CurrentVix { get; set; }
    public decimal VixChange { get; set; }
    public string MarketRegime { get; set; } = string.Empty;
    public decimal AdjustedDailyLossLimit { get; set; }
    public int AdjustedMaxPositions { get; set; }
    public RiskLevel RiskLevel { get; set; }
    public string RecommendedAction { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}
