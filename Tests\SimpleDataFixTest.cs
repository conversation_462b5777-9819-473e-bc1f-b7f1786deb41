using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public static class SimpleDataFixTest
{
    private static ServiceProvider? _serviceProvider;
    private static Microsoft.Extensions.Logging.ILogger? _logger;

    public static async Task RunSimpleDataFixTest()
    {
        Console.WriteLine("=== Simple Data Integration Fix Test ===\n");

        // Setup
        SetupServices();
        _logger = _serviceProvider!.GetRequiredService<ILoggerFactory>().CreateLogger("SimpleDataFixTest");

        try
        {
            // Test 1: Historical Data Service with Enhanced Synthetic Data
            await TestHistoricalDataService();

            // Test 2: Market Regime Analyzer VIX Calculation
            await TestVixCalculation();

            // Test 3: Volatility Calculations with Fallbacks
            await TestVolatilityCalculations();

            Console.WriteLine("\n=== Simple Data Integration Fix Test Completed Successfully ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Test failed with error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        finally
        {
            _serviceProvider?.Dispose();
        }
    }

    private static void SetupServices()
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .WriteTo.File($"logs/simple-data-fix-test-{DateTime.Now:yyyyMMdd}.txt")
            .CreateLogger();

        // Build configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        // Setup DI container
        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddSerilog());
        services.AddSingleton<ISecurityService, SecurityService>();
        services.AddSingleton<IConfigurationValidator, ConfigurationValidator>();
        services.AddSingleton<IGlobalExceptionHandler, GlobalExceptionHandler>();
        services.AddSingleton<IAlpacaService, AlpacaService>();
        services.AddSingleton<IHistoricalDataService, HistoricalDataService>();
        services.AddSingleton<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();

        _serviceProvider = services.BuildServiceProvider();
    }

    private static async Task TestHistoricalDataService()
    {
        Console.WriteLine("1. Testing Historical Data Service with Enhanced Synthetic Data...");

        var historicalDataService = _serviceProvider!.GetRequiredService<IHistoricalDataService>();

        // Test with various date ranges to ensure sufficient data
        var testCases = new[]
        {
            new { Symbol = "SPY", Days = 30, Description = "30-day SPY data" },
            new { Symbol = "QQQ", Days = 60, Description = "60-day QQQ data" },
            new { Symbol = "IWM", Days = 5, Description = "5-day IWM data (minimal)" }
        };

        foreach (var testCase in testCases)
        {
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-testCase.Days);

            var bars = await historicalDataService.GetHistoricalBarsAsync(
                testCase.Symbol, startDate, endDate);

            Console.WriteLine($"   {testCase.Description}: {bars.Count} bars retrieved");
            
            if (bars.Count > 0)
            {
                var firstBar = bars.First();
                var lastBar = bars.Last();
                Console.WriteLine($"     Date range: {firstBar.Date:yyyy-MM-dd} to {lastBar.Date:yyyy-MM-dd}");
                Console.WriteLine($"     Price range: {bars.Min(b => b.Low):F2} to {bars.Max(b => b.High):F2}");
                
                // Check for realistic volatility
                var returns = new List<decimal>();
                for (int i = 1; i < bars.Count; i++)
                {
                    var dailyReturn = Math.Abs((bars[i].Close - bars[i - 1].Close) / bars[i - 1].Close);
                    returns.Add(dailyReturn);
                }
                
                if (returns.Count > 0)
                {
                    var avgDailyVol = returns.Average() * 100;
                    Console.WriteLine($"     Average daily volatility: {avgDailyVol:F2}%");
                }
                
                Console.WriteLine($"     ✓ Data quality check passed");
            }
            else
            {
                Console.WriteLine($"     ⚠️ No data retrieved for {testCase.Symbol}");
            }
        }

        Console.WriteLine("   ✓ Historical Data Service Test Completed\n");
    }

    private static async Task TestVixCalculation()
    {
        Console.WriteLine("2. Testing VIX Calculation (should not show insufficient data warnings)...");

        var marketAnalyzer = _serviceProvider!.GetRequiredService<IMarketRegimeAnalyzer>();

        // Test VIX calculation multiple times to ensure consistency
        for (int i = 0; i < 3; i++)
        {
            var vix = await marketAnalyzer.GetVixAsync();
            Console.WriteLine($"   VIX calculation #{i + 1}: {vix:F2}%");
            
            // Verify VIX is within reasonable bounds
            if (vix >= 5 && vix <= 200)
            {
                Console.WriteLine($"     ✓ VIX value is within reasonable bounds");
            }
            else
            {
                Console.WriteLine($"     ⚠️ VIX value seems unrealistic: {vix:F2}%");
            }
        }

        Console.WriteLine("   ✓ VIX Calculation Test Completed\n");
    }

    private static async Task TestVolatilityCalculations()
    {
        Console.WriteLine("3. Testing Volatility Calculations with Fallback Handling...");

        var marketAnalyzer = _serviceProvider!.GetRequiredService<IMarketRegimeAnalyzer>();

        // Test market trend analysis (which uses volatility calculations internally)
        var symbols = new[] { "SPY", "QQQ", "IWM" };
        
        foreach (var symbol in symbols)
        {
            Console.WriteLine($"   Testing market trend for {symbol}...");
            var trend = await marketAnalyzer.GetMarketTrendAsync(symbol);
            Console.WriteLine($"     Market trend: {trend}");
            Console.WriteLine($"     ✓ Trend analysis completed without errors");
        }

        // Test market regime analysis
        Console.WriteLine("   Testing overall market regime analysis...");
        var regime = await marketAnalyzer.GetCurrentRegimeAsync();
        Console.WriteLine($"   Market Regime: {regime.OverallRegime}");
        Console.WriteLine($"   Volatility Regime: {regime.VolatilityRegime}");
        Console.WriteLine($"   Trend: {regime.Trend}");
        Console.WriteLine($"   Confidence: {regime.Confidence:P1}");
        Console.WriteLine($"   ✓ Market regime analysis completed");

        Console.WriteLine("   ✓ Volatility Calculations Test Completed\n");
    }
}
